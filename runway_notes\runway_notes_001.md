Comprehensive Prompting Guide for Runway
========================================

Introduction
------------

This guide provides an overview of how to craft meaningful prompts in Runway Gen-2. While there’s no single “correct” way to prompt, these tips and examples can help you create more cinematic and visually cohesive outputs.

* * *

1\. The Four W’s
----------------

*   **Who**: Who or what is in the scene?
*   **What**: What action or event is happening?
*   **Where**: Where is the setting or environment?
*   **When**: When in time does this take place (e.g., historical period, time of day, season)?

By including these details, you give the model important context to shape the composition and overall vibe of the final output.

* * *

2\. When to Keep It Short
-------------------------

*   Sometimes, a single-sentence prompt (e.g., “A serene mountain lake at dawn, cinematic lighting”) can be enough to produce strong results.
*   Short prompts work best when you already have a clear, simple concept and don’t need elaborate details.

* * *

3\. When to Get Creative
------------------------

*   Add nuances like camera angles, film references, and lighting styles for more cinematic outcomes.
*   Mention artists or specific aesthetics (e.g., “in the style of Studio Ghibli,” “inspired by Ansel Adams,” etc.) to evoke certain looks.
*   Emphasize mood or atmosphere (e.g., “moody,” “whimsical,” “post-apocalyptic,” “romantic”).

* * *

4\. Use References
------------------

*   Incorporate references to real places, famous paintings, or film stills to anchor the scene in a particular style or era.
*   This can help the model infer context, color palettes, and design elements more closely aligned with your vision.

* * *

5\. Understanding Your Role
---------------------------

Think of yourself as a **director** giving your camera crew instructions:

*   **Cinematography**: Decide on wide shots, close-ups, panoramic views, etc.
*   **Production Design**: Describe settings, props, and set pieces.
*   **Lighting**: Include terms like “soft morning light,” “harsh midday sun,” “golden hour glow,” “moody low-key lighting.”
*   **Scene Composition**: Where do subjects appear in the frame? Is there negative space?

* * *

6\. Writing Your Prompt
-----------------------

Depending on your goal, you can structure prompts differently:

### 6.1 Simple Structure

```css
[Main Subject], [Action or Context], [Environment], [Lighting], [Color or Style Cues]
```

**Example**:

```css
A solitary traveler walking across a vast desert, high noon sun, cinematic lighting, warm color palette
```

### 6.2 More Detailed Structure

```csharp
[Subject/Characters]
[Action or Scene Setup]
[Environment/Setting]
[Lighting/Mood/Atmosphere]
[References/Style Cues]
[Camera Techniques]
```

**Example**:

```csharp
An explorer standing atop a snowy mountain peak at dawn,
soft orange sunlight on the horizon,
atmospheric haze and mist,
inspired by National Geographic photography,
shot on a wide-angle lens for majestic scale
```

* * *

7\. Shots
---------

*   **Close-up**: Fills the frame with the subject’s face or object details.
*   **Medium Shot**: Shows the subject from the waist up; great for capturing gestures and emotions.
*   **Wide Shot / Long Shot**: Reveals the broader environment, conveying scale or solitude.

### Shots With References

*   Combine references with shot types:
    *   _“A medium shot of a medieval knight in a lush forest, cinematic lighting, reminiscent of Ridley Scott’s ‘Gladiator’ battle scenes.”_

* * *

8\. Sample Scenes (Text + Images)
---------------------------------

Below are various prompt examples (textual) plus a reference image or style note.

1.  **Ocean View**

    *   _Prompt:_ “A lone surfer paddling on calm waters at sunrise, pastel color palette, cinematic wide shot”
    *   _Result:_ \[Image example showing a surfer on gentle ocean waves with warm sky tones\]
2.  **City Street**

    *   _Prompt:_ “A bustling futuristic city street at night, neon lights, cyberpunk aesthetic”
    *   _Result:_ \[Image example depicting tall glowing skyscrapers, flying cars, vibrant neon signage\]
3.  **Portrait**

    *   _Prompt:_ “Portrait of a woman in soft window light, shallow depth of field, 35mm lens”
    *   _Result:_ \[Image example focusing on a woman’s face with soft bokeh background and moody lighting\]

_(Repeat as needed for each example you have. The key is to highlight the text prompt next to the visual result.)_

* * *

9\. Additional Prompts
----------------------

*   **Nature/Scenery:**
    *   _Prompt:_ “A lush rainforest canopy with morning mist, sun rays piercing through leaves, wide-angle shot”
*   **Fantasy/Surreal:**
    *   _Prompt:_ “A floating island with waterfalls cascading into the sky, surreal style, golden hour lighting”
*   **Emotional Tone:**
    *   _Prompt:_ “A single tear rolling down a child’s cheek in low-key lighting, a sense of hope in their eyes”

* * *

10\. Tips & Tricks
------------------

*   **Experiment:** Don’t hesitate to try different angles, time periods, or styles.
*   **Iterate:** Adjust your prompt if the output isn’t quite right—sometimes small changes (e.g., specifying “sunset” instead of “dusk”) can significantly alter results.
*   **Stay Consistent:** If you want multiple clips to share a theme or style, reuse certain prompt elements across each scene.
*   **Limit Overload:** Avoid overly long prompts; more detail isn’t always better. Strike a balance to guide the model without confusion.

* * *

11\. Resources & Further Reading
--------------------------------

*   [Runway Official Documentation](https://runwayml.com/)
*   Runway Gen\-2 Release Notes
*   Popular photography or film theory blogs for more cinematography ideas and references.

* * *

Conclusion
----------

Prompting is part art and part experimentation. By combining clear, concise direction with creative references, you can guide Runway Gen-2 to produce compelling, cinematic outputs. Keep refining your technique, explore different angles or narratives, and enjoy the process!